import { Body, Controller, Get, Post } from '@nestjs/common';
import { Printer } from 'pdf-to-printer';
import { PrintDto } from '../printer/dto/print.dto';
import { KitchenPrintDto } from '../printer/dto/kitchen-print.dto';
import { InvoicePrintDto } from '../printer/dto/invoice-print.dto';
import { ShiftPrintDto } from '../printer/dto/shift-print.dto';
import { PrinterService } from './printer.service';
import { IResponse } from 'src/common/interfaces/res.interface';

@Controller('printer')
export class PrinterController {
  constructor(private readonly printerService: PrinterService) { }

  @Get("getPrinters")
  getPrinters(): Promise<IResponse<Printer[]>> {
    return this.printerService.getPrinters();
  }

  @Post("print")
  print(
    @Body() body: PrintDto
  ): Promise<void> {
    return this.printerService.print(body);
  }

  @Post("print-kitchen")
  printKitchen(
    @Body() body: KitchenPrintDto
  ) {
    return this.printerService.printKitchen(body);
  }

  @Post("print-invoice")
  printInvoice(
    @Body() body: InvoicePrintDto
  ) {
    return this.printerService.printInvoice(body);
  }

  @Post("print-shift")
  printShift(
    @Body() body: ShiftPrintDto
  ) {
    return this.printerService.printShift(body);
  }
}

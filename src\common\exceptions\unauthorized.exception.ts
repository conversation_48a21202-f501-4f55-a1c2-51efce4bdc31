import { HttpStatus } from '@nestjs/common';
import { UN_AUTHENTICATED_MESSAGE } from '../constants/res.constants';
import { BaseException } from './base.exception';

export class CustomUnauthorizedException extends BaseException {
    constructor(message?: string, error?: string) {
        super(
            message ?? UN_AUTHENTICATED_MESSAGE,
            error ?? UN_AUTHENTICATED_MESSAGE,
            HttpStatus.UNAUTHORIZED,
        );
    }
}

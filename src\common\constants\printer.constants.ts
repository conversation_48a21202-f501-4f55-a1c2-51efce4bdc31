import { FilesEnums, FoldersEnums } from "./files.constants";

// pdfmake fonts
export var fonts = {
    Roboto: {
        normal: `${FoldersEnums.PUBLIC_FONTS}/${FilesEnums.TajawalRegular}`,
        bold: `${FoldersEnums.PUBLIC_FONTS}/${FilesEnums.TajawalBold}`,
        italics: `${FoldersEnums.PUBLIC_FONTS}/${FilesEnums.TajawalLight}`,
        bolditalics: `${FoldersEnums.PUBLIC_FONTS}/${FilesEnums.TajawalExtraLight}`,
    },
};

// Path: src/printers.ts
export const fileToSavePath = `${FoldersEnums.PUBLIC}/${FilesEnums.FILE_TO_PRINT}`;
export const logoPath = `./${FoldersEnums.PUBLIC_IMAGES}/${FilesEnums.LOGO}`;

export const RsSvgPlaceholder = "RS_SVG_PLACEHOLDER";
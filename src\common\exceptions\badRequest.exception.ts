import { HttpStatus } from '@nestjs/common';
import { BAD_REQUEST_MESSAGE } from '../constants/res.constants';
import { BaseException } from './base.exception';

export class CustomBadRequestException extends BaseException {
    constructor(message?: string, error?: string) {
        super(
            message ?? BAD_REQUEST_MESSAGE,
            error ?? BAD_REQUEST_MESSAGE,
            HttpStatus.BAD_REQUEST,
        );
    }
}

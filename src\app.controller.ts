import {
  Controller,
  Get,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { AppService } from './app.service';
import { UploadHelper } from './common/helpers/upload.helper';
import { FoldersEnums } from './common/constants/files.constants';
import { UploadedFileType } from './common/interfaces/upload.interface';
import { ResHelper } from './common/helpers/res.helper';
import {
  FILES_UPLOADED_SUCCESSFULLY,
  SUCCEEDED_MESSAGE,
} from './common/constants/res.constants';
import { IpHelper } from './common/helpers/ip.helper';

@Controller("local-server")
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Post('logo')
  @UseInterceptors(
    UploadHelper.uploadFile(FoldersEnums.PUBLIC_IMAGES, {
      fileName: 'logo',
      fileExtension: '.jpg',
      allowedTypes: ['image/jpeg'],
      isUnique: false,
    }),
  )
  uploadImage(@UploadedFile() file: UploadedFileType) {
    return ResHelper({
      message: FILES_UPLOADED_SUCCESSFULLY,
      data: {
        url: `/${FoldersEnums.PUBLIC_IMAGES}/${file.filename}`,
      },
    });
  }

  @Get('health')
  health() {
    return ResHelper({
      message: SUCCEEDED_MESSAGE,
      data: {
        status: 'OK',
        timestamp: new Date().toISOString(),
      },
    });
  }

  @Get('local-ip')
  getIp() {
    return ResHelper({
      message: SUCCEEDED_MESSAGE,
      data: IpHelper.getLocalIp(),
    });
  }
}

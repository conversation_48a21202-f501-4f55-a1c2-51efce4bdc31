import { Content } from 'pdfmake/interfaces';
import { formatTime } from '../../common/utils/date.utils';
import { IKitchenModel } from '../body/kitchen.body';
import { logoPath } from '../../common/constants/printer.constants';
import { PrinterUtils } from 'src/common/utils/printer.utils';

export const KitchenReceiptContent = (
    invoice: IKitchenModel,
): Content[] => {
    return [
        {
            image: logoPath,
            width: 150,
            height: 100,
        },
        ...PrinterUtils.optionalDocItem(!!invoice.organizationName, [
            {
                text: invoice.organizationName,
                bold: true,
                fontSize: 15,
            },
        ]),
        PrinterUtils.printerSingleDivider(),
        ...PrinterUtils.optionalDocItem(!!invoice.orderNumber, [
            PrinterUtils.textWithBorder(`Order # ${invoice.orderNumber}`),
        ]),
        ...PrinterUtils.optionalDocItem(!!invoice.orderTitle, [
            {
                text: invoice.orderTitle,
                bold: true,
                fontSize: 14,
                marginTop: 3,
            },
        ]),
        PrinterUtils.printerSingleDivider(),
        {
            columns: [
                {
                    text: formatTime().fullDate,
                    alignment: 'left',
                    marginBottom: 5,
                },
                {
                    text: 'التاريخ:',
                    alignment: 'right',
                },
            ],
        },
        ...PrinterUtils.optionalDocItem(!!invoice.orderType, [
            {
                columns: [
                    {
                        text: invoice.orderType,
                        alignment: 'left',
                        marginBottom: 5,
                    },
                    {
                        text: 'نوع الطلب:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        ...PrinterUtils.optionalDocItem(!!invoice.table, [
            {
                columns: [
                    {
                        text: invoice.table || '',
                        alignment: 'left',
                    },
                    {
                        text: 'الطاولة:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        PrinterUtils.printerSingleDivider(),
        {
            table: {
                headerRows: 1,
                widths: ['auto', '*'],
                body: [
                    [
                        { text: 'الكمية', bold: true },
                        { text: 'المنتج', bold: true, alignment: 'right' },
                    ],
                    ...invoice.items.map((product) => {
                        const additions = product.additions?.map((a, index) => a.quantity + ' * ' + a.name + (index < product.additions.length - 1 ? " ," : "")).join(' ');

                        return [
                            { text: product.quantity, fontSize: 8 },
                            [
                                {
                                    text: product.product,
                                    alignment: 'right',
                                    fontSize: 8,
                                    decoration: product.isDeleted ? 'lineThrough' : undefined,
                                },
                                !!product.additions?.length
                                    ? {
                                        text: ' ) ' + additions + ' ( ',
                                        alignment: 'right',
                                        fontSize: 7,
                                        decoration: product.isDeleted ? 'lineThrough' : undefined
                                    }
                                    : null,
                            ],
                        ];
                    }),
                ],
            },
        },
        ...PrinterUtils.optionalDocItem(!!invoice.note, [
            PrinterUtils.printerSingleDivider(),
            {
                text: `ملاحظة: ${invoice.note}`,
                alignment: 'right',
                fontSize: 10,
            },
        ]),
    ];
};

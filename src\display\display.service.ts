import { Injectable } from '@nestjs/common';
import { SerialPort } from 'serialport';
import { DisplayType } from './display.interface';

@Injectable()
export class DisplayService {
  private serialPort: SerialPort | null = null;

  async isPortAvailable(path: string) {
    return (await this.getPorts()).some(p => p.path === path);
  }

  async connect(path: string, baudRate: number) {
    return new Promise(async (resolve, reject) => {
      if (!await this.isPortAvailable(path)) {
        console.log('Port not available');
        reject({ success: false, message: 'Port not available' });
        return;
      }

      if (this.serialPort && this.serialPort.isOpen) {
        console.log('Serial port already open.');
        resolve({ success: true, message: 'Connected' });
        return;
      }

      this.serialPort = new SerialPort({
        path,
        baudRate,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
      });

      this.serialPort.on('open', () => {
        console.log(`Connected to display on ${path} at baud ${baudRate}`);
        resolve({ success: true, message: 'Connected' });
        return;
      });

      this.serialPort.on('error', (err) => {
        console.error('Serial port error:', err.message);
        reject({ success: false, message: err.message });
        return;
      });
    });
  }

  async disconnect() {
    if (this.serialPort && this.serialPort.isOpen) {
      this.serialPort.close();
      console.log('Serial port closed.');
    } else {
      console.log('Serial port not open.');
    }
  }

  async isConnected() {
    return !!(this.serialPort && this.serialPort.isOpen);
  }

  async getPorts() {
    return await SerialPort.list();
  }

  // Clear screen
  private clearScreen(): Buffer {
    return Buffer.from([0x0C]); // CLR command
  }

  // Build display command (ESC Q A <digits> CR)
  private buildDisplayCommand(value: string): Buffer {
    const digits = Buffer.from(value, 'ascii');
    return Buffer.concat([
      Buffer.from([0x1B, 0x51, 0x41]), // ESC Q A
      digits,
      Buffer.from([0x0D]),             // CR
    ]);
  }

  // Build label command (ESC s n)
  private buildLabelCommand(n: number): Buffer {
    return Buffer.from([0x1B, 0x73, n]);
  }

  // Send a value with a label
  async showValue(type: DisplayType, value: string) {
    return new Promise<void>((resolve, reject) => {
      if (!this.serialPort || !this.serialPort.isOpen) {
        reject(new Error('Serial port not connected.'));
        return;
      }

      // Pick label
      let typeCode = 0x30; // default off
      switch (type) {
        case 'price': typeCode = 0x31; break;    // 单价
        case 'total': typeCode = 0x32; break;    // 总计
        case 'received': typeCode = 0x33; break; // 收款
        case 'change': typeCode = 0x34; break;   // 找零
      }

      const clearCmd = this.clearScreen();
      const labelCmd = this.buildLabelCommand(typeCode);
      const displayCmd = this.buildDisplayCommand(value);

      this.serialPort.write(clearCmd);
      this.serialPort.write(labelCmd);
      this.serialPort.write(displayCmd, (err) => {
        if (err) reject(err);
        else {
          console.log(`Displayed [${type}] = ${value}`);
          resolve();
          return;
        }
      });
    });
  }
}

import { Alignment, Content } from 'pdfmake/interfaces';

export class PrinterUtils {
    static printerDoubleDivider = () => ({
        text: '='.repeat(31),
        bold: true,
    });

    static printerSingleDivider = (bold = false, marginTop = 0) => ({
        text: '-'.repeat(bold ? 72 : 78),
        bold,
        marginTop,
    });

    static optionalDocItem = (
        condition: boolean,
        item: Content[],
    ): Content[] => {
        return condition ? [...item] : [];
    };

    static textWithBorder = (val: string) => ({
        table: {
            widths: ['*', 'auto', '*'],
            body: [
                [
                    {
                        text: '',
                        border: [false, false, false, false],
                    },
                    {
                        text: val,
                        border: [true, true, true, true],
                        alignment: 'center',
                        fontSize: 18,
                        bold: true,
                        margin: [0, 5, 0, 0],
                    },
                    {
                        text: '',
                        border: [false, false, false, false],
                    },
                ],
            ],
        },
    });

    static multiText = (
        val: {
            text: string;
            alignment: Alignment;
            width?: '*' | 'auto';
            fontSize?: number;
        }[],
        backGround: boolean = false,
        fontSize: number = 7,
    ) => {
        return {
            layout: 'noBorders',
            style: {
                fillColor: backGround ? 'black' : '',
                color: backGround ? 'white' : '',
                fontSize: fontSize,
            },
            table: {
                widths: [...val.map((v) => v.width ?? '*')],
                body: [
                    [
                        ...val.map((v) => ({
                            text: v.text,
                            alignment: v.alignment,
                            fontSize: v.fontSize,
                        })),
                    ],
                ],
            },
        };
    };
}

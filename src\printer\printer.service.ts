import { Injectable } from '@nestjs/common';
import { Printer } from 'pdf-to-printer';
import { PrintDto } from '../printer/dto/print.dto';
import { debug } from 'util';
import { KitchenReceiptContent } from './content/kitchen-printer.content';
import { KitchenPrintDto } from '../printer/dto/kitchen-print.dto';
import { InvoicePrintDto } from '../printer/dto/invoice-print.dto';
import { InvoiceReceiptContent } from './content/invoice-printer.content';
import { ShiftPrintDto } from '../printer/dto/shift-print.dto';
import { ShiftReceiptContent } from './content/shift-printer.content';
import { PrinterHelper } from 'src/common/helpers/printer.helper';
import { PdfMakeHelper } from 'src/common/helpers/pdf-make.helper';
import { ResHelper } from 'src/common/helpers/res.helper';
import { IResponse } from 'src/common/interfaces/res.interface';
import { PRINTERS_FETCHED_SUCCESSFULLY } from 'src/common/constants/res.constants';

@Injectable()
export class PrinterService {
  async getPrinters(): Promise<IResponse<Printer[]>> {
    try {
      const data = await PrinterHelper.getAllPrinters();

      return ResHelper({
        message: PRINTERS_FETCHED_SUCCESSFULLY,
        data,
      });
    } catch (error) {
      debug('Error in getPrinters:', error);
      throw error;
    }
  }

  async print({ printerId, body }: PrintDto): Promise<void> {
    try {
      const content = PdfMakeHelper.handleRsSvgPrint(body);
      await PrinterHelper.saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in print:', error);
      throw error;
    }
  }

  async printKitchen({ printerId, body }: KitchenPrintDto): Promise<void> {
    try {
      const content = KitchenReceiptContent(body);
      await PrinterHelper.saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in printKitchen:', error);
      throw error;
    }
  }

  async printInvoice({ printerId, body }: InvoicePrintDto): Promise<void> {
    try {
      const content = InvoiceReceiptContent(body);
      await PrinterHelper.saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in printInvoice:', error);
      throw error;
    }
  }

  async printShift({ printerId, body }: ShiftPrintDto): Promise<void> {
    try {
      const content = ShiftReceiptContent(body);
      await PrinterHelper.saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in printInvoice:', error);
      throw error;
    }
  }
}

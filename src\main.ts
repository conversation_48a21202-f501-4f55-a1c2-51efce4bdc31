import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import * as bodyParser from 'body-parser';
import helmet from 'helmet';
import { isPkg } from './common/utils/global.utils';
import { NestExpressApplication } from '@nestjs/platform-express';
import { SystemHelper } from './common/helpers/system.helper';
import { FoldersEnums } from './common/constants/files.constants';
import { handleFixFileStructure } from './handler';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  try {
    handleFixFileStructure();
  } catch (error) {
    console.error('❌ Error handling file structure:', error);
  }

  app.useStaticAssets(SystemHelper.getPath({ path: FoldersEnums.PUBLIC }), {
    prefix: `/${FoldersEnums.PUBLIC}/`,
  });
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
  app.setGlobalPrefix('api/v1');
  app.use(helmet());
  app.enableCors({
    methods: 'GET,PATCH,POST',
    origin: !isPkg
      ? '*'
      : ['https://royaa-restaurant.vercel.app', 'http://localhost:5173'],
    allowedHeaders: ['Content-Type, Accept', 'Authorization'],
  });
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      stopAtFirstError: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  await app.listen(3007, '0.0.0.0');
}
bootstrap();

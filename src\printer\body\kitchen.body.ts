export interface IKitchenAdditionModel {
    quantity: string;
    name: string;
}

export interface IKitchenItemModel {
    quantity: string;
    product: string;
    isDeleted?: boolean;
    additions?: IKitchenAdditionModel[];
}

export interface IKitchenModel {
    organizationName?: string;
    orderNumber?: string;
    orderTitle?: string;
    orderType?: string;
    table?: string;
    items: IKitchenItemModel[];
    note?: string;
}
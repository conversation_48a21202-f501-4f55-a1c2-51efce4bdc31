import os from "os";

export class IpHelper {
    static getLocalIp(): string | undefined {
        const interfaces = os.networkInterfaces();

        for (const name of Object.keys(interfaces)) {
            for (const iface of interfaces[name] || []) {
                if (iface.family === "IPv4" && !iface.internal) {
                    return iface.address; // example: "*************"
                }
            }
        }
    }
}
import { DataSource, DataSourceOptions } from 'typeorm';
import { config } from 'dotenv';
import { SystemHelper } from 'src/common/helpers/system.helper';
config();

export const dataSourceOptions: DataSourceOptions = {
    type: 'sqlite',
    database: SystemHelper.getPath({ path: 'db.sqlite' }),
    entities: ['dist/**/*.entity{.ts,.js}'],
    synchronize: true,
    // ssl: isDev ? false : { rejectUnauthorized: false },
};

const dataSource: DataSource = new DataSource(dataSourceOptions);
export default dataSource;

import { PrintOptions, Printer, print } from "pdf-to-printer";
import { Content } from "pdfmake/interfaces";
import { fileToSavePath } from "../constants/printer.constants";
import { isPkg } from "../utils/global.utils";
import { FilesEnums } from "../constants/files.constants";
import { PrintersCore } from "../core/printers.core";
import { PdfMakeHelper } from "./pdf-make.helper";

export class PrinterHelper {
    // handle specific printer to print and its options
    private static handlePrinterOptions(printerId: string): PrintOptions {
        return {
            printer: printerId,
            scale: "shrink",
            sumatraPdfPath: isPkg ? `./${FilesEnums.SUMATRA_PDF}` : undefined,
        };
    }

    // start printing process
    static async startPrint(printerId: string, fileToPrintPath: string) {
        try {
            const options = this.handlePrinterOptions(printerId);
            await print(fileToPrintPath, options);
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    static async saveAndPrint(
        printerId: string,
        content: Content[] | Content,
        filePath: string = fileToSavePath
    ) {
        try {
            await PdfMakeHelper.savePDF(content, filePath);
            await this.startPrint(printerId, filePath);
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    // get all printers names and ids
    static getAllPrinters = async (): Promise<Printer[]> => {
        try {
            const printers = await PrintersCore.getPrinterNames();
            return printers.map(printer => ({
                deviceId: printer,
                name: printer,
                paperSizes: []
            }));
        } catch (error) {
            console.error(error);
            throw error;
        }
    };
}
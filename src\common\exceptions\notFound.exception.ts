import { HttpStatus } from '@nestjs/common';
import { NOT_FOUND_MESSAGE } from '../constants/res.constants';
import { BaseException } from './base.exception';

export class CustomNotFoundException extends BaseException {
    constructor(message?: string, error?: string) {
        super(
            message ?? NOT_FOUND_MESSAGE,
            error ?? NOT_FOUND_MESSAGE,
            HttpStatus.NOT_FOUND,
        );
    }
}

import { Content } from 'pdfmake/interfaces';
import { IShiftModel } from '../body/shift.body';
import { formatTime } from '../../common/utils/date.utils';
import { logoPath } from '../../common/constants/printer.constants';
import { PrinterUtils } from 'src/common/utils/printer.utils';

export const ShiftReceiptContent = (invoice: IShiftModel): Content[] => {
    return [
        {
            image: logoPath,
            width: 150,
            height: 100,
        },
        {
            text: 'إقفال الوردية',
            bold: true,
            fontSize: 12,
            marginBottom: 5,
        },

        PrinterUtils.multiText([
            { text: ". Sales Summary", alignment: "left" },
            { text: ". ملخص المبيعات", alignment: "right" },
        ], true, 10),
        PrinterUtils.multiText([
            { text: "Invoices", alignment: "left" },
            { text: invoice.ordersCount.toString(), alignment: "center", width: "auto" },
            { text: "عدد الفواتير", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "T.Discount", alignment: "left" },
            { text: invoice.discountAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إجمالي الخصم", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "T.Sales", alignment: "left" },
            { text: invoice.totalAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إجمالي المبيعات", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "Cash", alignment: "left" },
            { text: invoice.cashAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "مبيعات الكاش", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "Network", alignment: "left" },
            { text: invoice.networkAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "مبيعات الشبكة", alignment: "right" },
        ]),

        invoice.paymentAmounts.map((payment) => {
            return [
                ...PrinterUtils.optionalDocItem(payment.amount > 0, [
                    PrinterUtils.printerSingleDivider(),
                    PrinterUtils.multiText([
                        { text: payment.nameEn, alignment: "left" },
                        { text: payment.amount.toFixed(2), alignment: "center", width: "auto" },
                        { text: payment.name, alignment: "right" },
                    ]),
                ])]
        }),

        PrinterUtils.multiText([
            { text: ". Accounting", alignment: "left" },
            { text: ". الحركة المالية", alignment: "right" },
        ], true, 10),
        PrinterUtils.multiText([
            { text: "Start Shift", alignment: "left" },
            { text: invoice.startAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "رصيد بداية الوردية", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "Close Shift", alignment: "left" },
            { text: invoice.endAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "رصيد الإغلاق", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "Addition", alignment: "left" },
            { text: invoice.additionAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "الزيادة", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "Shortage", alignment: "left" },
            { text: invoice.shortageAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "العجز", alignment: "right" },
        ]),

        PrinterUtils.multiText([
            { text: ". Other", alignment: "left" },
            { text: ". أخرى", alignment: "right" },
        ], true, 10),
        PrinterUtils.multiText([
            { text: "Start Time", alignment: "left" },
            { text: formatTime(new Date(invoice.startTime)).fullDate, alignment: "center", width: "auto" },
            { text: "وقت فتح الوردية", alignment: "right" },
        ]),
        PrinterUtils.printerSingleDivider(),
        PrinterUtils.multiText([
            { text: "End Time", alignment: "left" },
            { text: formatTime(new Date(invoice.endTime)).fullDate, alignment: "center", width: "auto" },
            { text: "وقت غلق الوردية", alignment: "right" },
        ]),
    ];
};

import { exec } from 'child_process';
import { promisify } from 'util';

export class PrintersCore {
    static getPrinterNamesWithWmic = async (): Promise<string[]> => {
        const execAsync = promisify(exec);
        try {
            const { stdout } = await execAsync('wmic printer get name');
            const printers = stdout.split('\n').slice(1).map(l => l.trim()).filter(Boolean);

            return printers;
        } catch (error) {
            console.error('Error getting printers with wmic:', error);
            return [];
        }
    };

    static getPrinterNamesWithPowerShell = async (): Promise<string[]> => {
        const execAsync = promisify(exec);
        try {
            const { stdout } = await execAsync('powershell -Command \"Get-Printer | Select-Object -ExpandProperty Name\"');
            const printers = stdout.split(/\r?\n/).map(l => l.trim()).filter(Boolean);

            return printers;
        } catch (error) {
            console.error('Error getting printers with powershell:', error);
            return [];
        }
    };

    static getPrinterNames = async (): Promise<string[]> => {
        try {
            const printers = await this.getPrinterNamesWithWmic();
            return printers;
        } catch (error) {
            try {
                const printers = await this.getPrinterNamesWithPowerShell();;
                return printers;
            } catch (error) {
                console.error('Error getting printers:', error);
                return [];
            }
        }
    };
}

import { HttpStatus } from '@nestjs/common';
import { FORBIDDEN_MESSAGE } from '../constants/res.constants';
import { BaseException } from './base.exception';

export class CustomForbiddenException extends BaseException {
    constructor(message?: string, error?: string) {
        super(
            message ?? FORBIDDEN_MESSAGE,
            error ?? FORBIDDEN_MESSAGE,
            HttpStatus.FORBIDDEN,
        );
    }
}

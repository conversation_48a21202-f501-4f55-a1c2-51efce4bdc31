import { dirname, join } from 'path';
import { existsSync, mkdirSync, statSync, copyFileSync, rmSync, cpSync } from 'fs';
import { isPkg } from '../utils/global.utils';

export class SystemHelper {
    static getPath(options?: { path?: string; createIfNotExists?: boolean }) {
        const { path = '', createIfNotExists = true } = options || {};

        const baseDir = isPkg ? dirname(process.execPath) : process.cwd();
        const dir = join(baseDir, path);
        if (createIfNotExists && !existsSync(dir)) mkdirSync(dir, { recursive: true });

        return dir;
    }

    static isPathExists(path: string) {
        return existsSync(path);
    }

    static removePath(path: string) {
        if (this.isPathExists(path)) {
            rmSync(path, { recursive: true, force: true });
        }
    }

    static copyFile(source: string, destination: string) {
        if (!this.isPathExists(source)) return;

        const stats = statSync(source);

        if (stats.isDirectory()) {
            // ✅ Copy entire directory recursively
            cpSync(source, destination, { recursive: true });
        } else {
            // ✅ Copy single file
            const destDir = dirname(destination);
            if (!existsSync(destDir)) mkdirSync(destDir, { recursive: true });
            copyFileSync(source, destination);
        }
    }
}

import { FilesEnums, FoldersEnums } from "./common/constants/files.constants";
import { SystemHelper } from "./common/helpers/system.helper";
import * as path from 'path';

export const handleFixFileStructure = () => {
    const uploadsPath = SystemHelper.getPath({ path: FoldersEnums.UPLOADS, createIfNotExists: false });
    const imagesPath = SystemHelper.getPath({ path: FoldersEnums.IMAGES, createIfNotExists: false });
    const fontsPath = SystemHelper.getPath({ path: FoldersEnums.FONTS, createIfNotExists: false });
    const publicPath = SystemHelper.getPath({ path: FoldersEnums.PUBLIC });
    const publicImagesPath = SystemHelper.getPath({ path: FoldersEnums.PUBLIC_IMAGES });
    const publicIconsPath = SystemHelper.getPath({ path: FoldersEnums.PUBLIC_ICONS });

    try {
        // check if "images" folder exists
        if (SystemHelper.isPathExists(imagesPath)) {
            const logoSource = path.join(imagesPath, FilesEnums.LOGO);
            const logoDestination = path.join(publicImagesPath, FilesEnums.LOGO);

            // copy logo.jpg if exists
            if (SystemHelper.isPathExists(logoSource)) {
                SystemHelper.copyFile(logoSource, logoDestination);
                console.log('✅ Copied logo.jpg to public folder.');
            } else {
                console.warn('⚠️ No logo.jpg found in images folder.');
            }

            // copy icon.ico if exists
            const iconSource = path.join(imagesPath, FilesEnums.ICON);
            const iconDestination = path.join(publicIconsPath, FilesEnums.ICON);

            if (SystemHelper.isPathExists(iconSource)) {
                SystemHelper.copyFile(iconSource, iconDestination);
                console.log('✅ Copied icon.ico to public folder.');
            } else {
                console.warn('⚠️ No icon.ico found in images folder.');
            }

            // delete images folder
            SystemHelper.removePath(imagesPath);
            console.log('🗑️ Deleted images folder.', imagesPath);
        }

        // check if "fonts" folder exists
        if (SystemHelper.isPathExists(fontsPath)) {
            const fontsDestination = path.join(publicPath, FoldersEnums.FONTS);

            // copy fonts if exists
            if (SystemHelper.isPathExists(fontsPath)) {
                SystemHelper.copyFile(fontsPath, fontsDestination);
                console.log('✅ Copied fonts to public folder.');
            } else {
                console.warn('⚠️ No fonts found in images folder.');
            }

            // delete fonts folder
            SystemHelper.removePath(fontsPath);
            console.log('🗑️ Deleted fonts folder.');
        }

        // delete uploads folder if exists
        if (SystemHelper.isPathExists(uploadsPath)) {
            SystemHelper.removePath(uploadsPath);
            console.log('🗑️ Deleted uploads folder.');
        }
    } catch (err) {
        console.error('❌ Error handling image/upload folders:', err);
    }
}
import { HttpStatus } from '@nestjs/common';
import { SUCCEEDED_MESSAGE } from '../constants/res.constants';
import { IResponse } from '../interfaces/res.interface';

export const ResHelper = <T>(resOptions?: IResponse<T | any>) => {
    const res = {
        statusCode: resOptions.statusCode ?? HttpStatus.OK,
        message: resOptions.message ?? SUCCEEDED_MESSAGE,
        success: resOptions.success ?? true,
        access_token: resOptions.access_token,
        data: resOptions.data,
        error: resOptions.error,
        total: resOptions.total,
        page: resOptions.page,
        limit: resOptions.limit,
        totalPages: resOptions.totalPages,
    };

    if (!resOptions.access_token) delete res.access_token;
    if (!resOptions.data) delete res.data;
    if (!resOptions.error) delete res.error;
    if (!resOptions.total) delete res.total;
    if (!resOptions.page) delete res.page;
    if (!resOptions.limit) delete res.limit;
    if (!resOptions.totalPages) delete res.totalPages;

    return res;
};

import { Controller, Get, Query } from '@nestjs/common';
import { DisplayService } from './display.service';
import { DisplayType } from './display.interface';

@Controller('display')
export class DisplayController {
  constructor(private readonly displayService: DisplayService) { }

  @Get('connect')
  async connect(
    @Query('path') path: string,
    @Query('baudRate') baudRate: number
  ) {
    try {
      const res = await this.displayService.connect(path, baudRate);
      return { success: true, data: res };
    } catch (err) {
      return { success: false, message: err.message || err };
    }
  }

  @Get('disconnect')
  disconnect() {
    this.displayService.disconnect();
  }

  @Get('isConnected')
  async isConnected() {
    const data = await this.displayService.isConnected();
    console.log('isConnected', data);
    return { success: true, data };
  }

  @Get('ports')
  async getPorts() {
    try {
      const data = await this.displayService.getPorts();
      return { success: true, data };
    } catch (err) {
      return { success: false, message: err.message || err };
    }
  }

  @Get('price')
  async show(
    @Query('price') price: string,
    @Query('type') type: DisplayType,
  ) {
    try {
      await this.displayService.showValue(type, price);
      return { success: true, message: `Displayed [${type}] = ${price}` };
    } catch (err) {
      return { success: false, message: err.message || err };
    }
  }
}

{"name": "printer-controller-nest", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "pkg": "npm run build && pkg ."}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.4.20", "@types/pdfmake": "^0.2.11", "@types/serialport": "^10.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "helmet": "^8.1.0", "multer": "^2.0.2", "pdf-to-printer": "^5.6.0", "pdfmake": "^0.2.18", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "serialport": "^13.0.0", "sqlite3": "^5.1.7", "typeorm": "^0.3.27"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^2.0.0", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "bin": {"my-nest-app": "dist/main.js"}, "pkg": {"scripts": "dist/**/*.js", "assets": ["public/**/*", "node_modules/@foliojs-fork/linebreak/src/*", "node_modules/pdf-to-printer/dist/*", "node_modules/@serialport/bindings-cpp/prebuilds/**/*"], "targets": ["node18-win-x64"], "outputPath": "build"}}
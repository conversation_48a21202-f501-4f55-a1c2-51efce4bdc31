import { HttpStatus } from '@nestjs/common';
import { INTERNAL_SERVER_MESSAGE } from '../constants/res.constants';
import { BaseException } from './base.exception';

export class CustomInternalServerException extends BaseException {
    constructor(message?: string, error?: string) {
        super(
            message ?? INTERNAL_SERVER_MESSAGE,
            error ?? INTERNAL_SERVER_MESSAGE,
            HttpStatus.INTERNAL_SERVER_ERROR,
        );
    }
}
